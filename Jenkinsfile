pipeline {
  agent none
  
  environment {
    HARBOR_REGISTRY = 'harbor.zhixin.asia'
    FRONTEND_IMAGE = "${HARBOR_REGISTRY}/ops-platform/frontend"
    BACKEND_IMAGE = "${HARBOR_REGISTRY}/ops-platform/backend"
    NODE_ENV = 'production'
    PYTHON_ENV = 'production'
  }
  
  stages {
    stage('Checkout Code') {
      agent any
      steps {
        git branch: 'dev', 
            credentialsId: 'gitea', 
            url: 'https://git.zhixin.asia/221900264/OPS-Platform'
        script {
          env.GIT_COMMIT_SHORT = sh(
            script: 'git rev-parse --short HEAD',
            returnStdout: true
          ).trim()
          env.BUILD_TAG = "ops-platform-${env.BUILD_NUMBER}-${env.GIT_COMMIT_SHORT}"
        }
        echo "Git commit: ${env.GIT_COMMIT_SHORT}"
        echo "Build tag: ${env.BUILD_TAG}"
        
        stash includes: '**', name: 'source-code', useDefaultExcludes: true  // 添加排除规则
      }
    }

    stage('Frontend Build') {
      agent {
        kubernetes {
          yaml '''
apiVersion: v1
kind: Pod
spec:
  containers:
  - name: node
    image: node:22-alpine
    command: ['cat']
    tty: true
    resources:
      requests:
        memory: "1Gi"
        cpu: "1000m"
      limits:
        memory: "2Gi"
        cpu: "2000m"
'''
        }
      }
      steps {
        container('node') {
          unstash 'source-code'
          
          sh '''
            echo "=== 前端构建开始 ==="
            cd frontend

            # 显示环境信息
            echo "Node.js: $(node --version)"
            echo "npm: $(npm --version)"

            # 使用国内镜像源
            npm config set registry https://registry.npmmirror.com

            # 保留lock文件保证一致性
            echo "安装依赖..."
            npm ci --verbose  # 使用ci代替install

            # 类型检查（严格模式）
            echo "执行类型检查..."
            NODE_OPTIONS="--max-old-space-size=1536" npx vue-tsc --noEmit --skipLibCheck || {
              echo "❌ 类型检查失败！终止构建"
              exit 1
            }

            # 构建应用
            echo "开始构建..."
            NODE_OPTIONS="--max-old-space-size=1536" npm run build

            # 验证构建结果
            if [ ! -d "dist" ]; then
              echo "❌ 构建失败：dist目录不存在"
              exit 1
            fi
            echo "构建产物大小: $(du -sh dist/ | awk '{print $1}')"
          '''
        }
        // 新增：存储前端构建产物
        stash includes: 'frontend/dist/**', name: 'frontend-dist'
      }
    }

    stage('Backend Build & Test') {
      agent {
        kubernetes {
          yaml '''
apiVersion: v1
kind: Pod
spec:
  containers:
  - name: python
    image: python:3.11-slim
    command: ['cat']
    tty: true
    resources:
      requests:
        memory: "1Gi"
        cpu: "1000m"
      limits:
        memory: "2Gi"
        cpu: "2000m"
    env:
    - name: PYTHONUNBUFFERED
      value: "1"
'''
        }
      }
      steps {
        container('python') {
          unstash 'source-code'
          
          sh '''
            echo "=== 后端构建开始 ==="
            cd backend

            # 配置镜像源
            pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple
            
            # 安装系统依赖
            apt-get update && apt-get install -y gcc g++ libpq-dev libffi-dev
            
            # 安装uv并同步依赖
            pip install uv
            uv pip install -r requirements.txt  # 明确指定依赖文件
            
            # 代码检查
            echo "运行代码质量检查..."
            uv run black --check --diff . || true
            uv run isort --check-only --diff . || true
            uv run flake8 . || true
            
            # 运行测试（失败时继续收集结果）
            echo "运行测试..."
            uv run pytest tests/ \
              --maxfail=1 \
              --disable-warnings \
              --cov=app \
              --cov-report=term-missing \
              --junitxml=test-results.xml || \
              echo "⚠️ 测试失败（继续流程）"
            
            # 保存测试报告
            mkdir -p reports
            mv test-results.xml reports/
          '''
        }
        
        // 新增：归档测试报告
        archiveArtifacts artifacts: 'backend/reports/test-results.xml', allowEmptyArchive: true
      }
    }

    stage('Build & Push Docker Images') {
      agent {
        kubernetes {
          yaml '''
apiVersion: v1
kind: Pod
spec:
  containers:
  - name: docker
    image: docker:20.10-dind  // 使用dind镜像
    securityContext:
      privileged: false  // 禁用特权模式
    env:
    - name: DOCKER_TLS_CERTDIR
      value: ""
    volumeMounts:
    - mountPath: /var/run/docker.sock
      name: docker-sock
  volumes:
  - name: docker-sock
    hostPath:
      path: /var/run/docker.sock
'''
        }
      }
      steps {
        container('docker') {
          // 恢复原始代码和构建产物
          unstash 'source-code'
          unstash 'frontend-dist'  // 关键修复：注入前端构建产物
          
          withCredentials([
            usernamePassword(
              credentialsId: 'dev-harbor', 
              passwordVariable: 'HARBOR_PASS', 
              usernameVariable: 'HARBOR_USER'
            )
          ]) {
            sh '''
              echo "=== Docker构建开始 ==="
              
              # 登录Harbor
              echo "$HARBOR_PASS" | docker login $HARBOR_REGISTRY -u "$HARBOR_USER" --password-stdin
              
              # 前端镜像构建
              echo "构建前端镜像..."
              docker build -t $FRONTEND_IMAGE:$BUILD_TAG \
                -t $FRONTEND_IMAGE:latest \
                -f frontend/Dockerfile \
                ./frontend
              
              # 后端镜像构建
              echo "构建后端镜像..."
              docker build -t $BACKEND_IMAGE:$BUILD_TAG \
                -t $BACKEND_IMAGE:latest \
                -f backend/Dockerfile \
                ./backend
              
              # 推送镜像
              echo "推送镜像..."
              docker push $FRONTEND_IMAGE:$BUILD_TAG
              docker push $FRONTEND_IMAGE:latest
              docker push $BACKEND_IMAGE:$BUILD_TAG
              docker push $BACKEND_IMAGE:latest
              
              # 安全清理
              docker logout $HARBOR_REGISTRY
              unset HARBOR_PASS
            '''
          }
        }
      }
    }
  }
  
  post {
    always {
      echo "构建标签: ${env.BUILD_TAG}"
      
      // 安全清理凭证
      withCredentials([usernamePassword(credentialsId: 'dev-harbor', passwordVariable: 'X', usernameVariable: 'Y')]) {
        sh 'unset Y X'
      }
    }
    
    success {
      echo "✅ 构建成功！"
      echo "前端镜像: ${FRONTEND_IMAGE}:${env.BUILD_TAG}"
      echo "后端镜像: ${BACKEND_IMAGE}:${env.BUILD_TAG}"
    }
    
    failure {
      echo "❌ 构建失败！"
      // 实际通知示例（需配置）：
      // dingtalk robot: ...
      // email: ...
    }
    
    cleanup {
      cleanWs()
      echo "工作空间已清理"
    }
  }
}