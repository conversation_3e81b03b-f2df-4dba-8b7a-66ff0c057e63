// file-saver 类型声明文件
// 解决 TypeScript 模块解析问题

declare module 'file-saver' {
  /**
   * FileSaver.js implements the saveAs() FileSaver interface in browsers that do not natively support it.
   * @param data - The actual file data blob or URL.
   * @param filename - The optional name of the file to be downloaded. If omitted, the name used in the file data will be used. If none is provided "download" will be used.
   * @param options - Optional FileSaver.js config
   */
  export function saveAs(data: Blob | string, filename?: string, options?: FileSaverOptions): void;

  /**
   * FileSaver.js implements the saveAs() FileSaver interface in browsers that do not natively support it.
   * @param data - The actual file data blob or URL.
   * @param filename - The optional name of the file to be downloaded. If omitted, the name used in the file data will be used. If none is provided "download" will be used.
   * @param disableAutoBOM - Optional & defaults to `true`. Set to `false` if you want FileSaver.js to automatically provide Unicode text encoding hints
   * @deprecated use `{ autoBom: false }` as the third argument
   */
  export function saveAs(data: Blob | string, filename?: string, disableAutoBOM?: boolean): void;

  export interface FileSaverOptions {
    /**
     * Automatically provide Unicode text encoding hints
     * @default false
     */
    autoBom: boolean;
  }
}
