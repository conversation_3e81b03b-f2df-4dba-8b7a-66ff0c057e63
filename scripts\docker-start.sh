#!/bin/bash

# OPS平台Docker启动脚本
# 使用方法: ./scripts/docker-start.sh [环境]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查Docker是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    print_message "Docker环境检查通过"
}

# 检查环境变量文件
check_env_file() {
    if [ ! -f .env ]; then
        print_warning "未找到.env文件，将使用默认配置"
        if [ -f env.example ]; then
            print_message "复制env.example为.env"
            cp env.example .env
        fi
    else
        print_message "找到.env配置文件"
    fi
}

# 停止现有服务
stop_services() {
    print_step "停止现有服务..."
    docker-compose down --remove-orphans
}

# 清理资源
cleanup() {
    print_step "清理Docker资源..."
    docker system prune -f
}

# 构建镜像
build_images() {
    print_step "构建Docker镜像..."
    docker-compose build --no-cache
}

# 启动服务
start_services() {
    print_step "启动服务..."
    docker-compose up -d
    
    print_message "等待服务启动..."
    sleep 10
    
    # 检查服务状态
    print_step "检查服务状态..."
    docker-compose ps
}

# 运行数据库迁移
run_migrations() {
    print_step "运行数据库迁移..."
    docker-compose run --rm migration
}

# 显示访问信息
show_access_info() {
    print_message "服务启动完成！"
    echo ""
    echo "访问地址:"
    echo "  前端应用: http://localhost"
    echo "  后端API:  http://localhost:8000"
    echo "  数据库:   localhost:5432"
    echo "  Redis:    localhost:6379"
    echo ""
    echo "查看日志: docker-compose logs -f [服务名]"
    echo "停止服务: docker-compose down"
    echo ""
}

# 主函数
main() {
    local environment=${1:-production}
    
    print_message "开始部署OPS平台 (环境: $environment)"
    echo ""
    
    check_docker
    check_env_file
    stop_services
    cleanup
    build_images
    start_services
    run_migrations
    show_access_info
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
