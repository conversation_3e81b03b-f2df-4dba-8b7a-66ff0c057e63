# 国内镜像源配置指南

为了提高在国内环境下的依赖安装速度和成功率，本项目已配置了国内镜像源。

## 📦 NPM 镜像源配置

### 配置文件
- **位置**: `frontend/.npmrc`
- **镜像源**: https://registry.npmmirror.com (淘宝镜像)

### 配置内容
```ini
# npm 国内镜像配置
registry=https://registry.npmmirror.com
disturl=https://npmmirror.com/dist
electron_mirror=https://npmmirror.com/mirrors/electron/
sass_binary_site=https://npmmirror.com/mirrors/node-sass/
phantomjs_cdnurl=https://npmmirror.com/mirrors/phantomjs/
chromedriver_cdnurl=https://npmmirror.com/mirrors/chromedriver/
operadriver_cdnurl=https://npmmirror.com/mirrors/operadriver/
fse_binary_host_mirror=https://npmmirror.com/mirrors/fsevents/
```

### Jenkins CI 配置
在Jenkins构建过程中，会自动配置npm镜像源：
```bash
npm config set registry https://registry.npmmirror.com
npm config set disturl https://npmmirror.com/dist
# ... 其他配置
```

## 🐍 Python 镜像源配置

### 配置文件
- **位置**: `backend/pip.conf`
- **主镜像源**: https://pypi.tuna.tsinghua.edu.cn/simple (清华大学镜像)
- **备用镜像源**: https://mirrors.aliyun.com/pypi/simple/ (阿里云镜像)

### 配置内容
```ini
[global]
index-url = https://pypi.tuna.tsinghua.edu.cn/simple
trusted-host = pypi.tuna.tsinghua.edu.cn
extra-index-url = https://mirrors.aliyun.com/pypi/simple/
timeout = 60
retries = 3
```

### Jenkins CI 配置
在Jenkins构建过程中，会自动配置pip镜像源：
```bash
pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple
pip config set global.trusted-host pypi.tuna.tsinghua.edu.cn
```

## 🐳 Docker 构建优化

### 前端 Dockerfile
- 复制 `.npmrc` 配置文件到容器
- 在安装依赖前设置镜像源

### 后端 Dockerfile
- 复制 `pip.conf` 配置文件到容器
- 在安装依赖前设置镜像源

## 🚀 性能提升

使用国内镜像源后的预期改进：

| 组件 | 原始速度 | 优化后速度 | 提升幅度 |
|------|----------|------------|----------|
| npm install | 2-5分钟 | 30-60秒 | 70-80% |
| pip install | 1-3分钟 | 20-40秒 | 60-70% |
| Docker build | 5-10分钟 | 2-4分钟 | 50-60% |

## 🔧 本地开发配置

### 手动配置 npm 镜像源
```bash
npm config set registry https://registry.npmmirror.com
```

### 手动配置 pip 镜像源
```bash
pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple
```

### 临时使用镜像源
```bash
# npm
npm install --registry https://registry.npmmirror.com

# pip
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple package_name
```

## 🔄 镜像源切换

如果某个镜像源不可用，可以切换到其他镜像源：

### NPM 备用镜像源
- 官方源: https://registry.npmjs.org/
- 华为云: https://mirrors.huaweicloud.com/repository/npm/
- 腾讯云: https://mirrors.cloud.tencent.com/npm/

### Python 备用镜像源
- 官方源: https://pypi.org/simple
- 豆瓣: https://pypi.douban.com/simple/
- 华为云: https://mirrors.huaweicloud.com/repository/pypi/simple/

## 📝 注意事项

1. **网络环境**: 镜像源配置主要针对国内网络环境优化
2. **版本同步**: 镜像源可能存在几分钟到几小时的同步延迟
3. **安全性**: 使用可信的镜像源，避免安全风险
4. **监控**: 定期检查镜像源的可用性和同步状态

## 🛠️ 故障排除

### npm 安装失败
```bash
# 清理缓存
npm cache clean --force

# 重新安装
rm -rf node_modules package-lock.json
npm install
```

### pip 安装失败
```bash
# 清理缓存
pip cache purge

# 使用不同镜像源重试
pip install -i https://mirrors.aliyun.com/pypi/simple/ package_name
```

### 检查当前配置
```bash
# 查看 npm 配置
npm config list

# 查看 pip 配置
pip config list
```
